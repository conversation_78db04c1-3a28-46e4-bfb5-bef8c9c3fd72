import { fileURLToPath, URL } from "url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import injectExternals from "vite-plugin-inject-externals";
import CDNSettings from "@incomnetworking/pagebuild-cdn-settings";

function camelize(str) {
  let arr = str.split("-");
  let capital = arr.map(
    (item) => item.charAt(0).toUpperCase() + item.slice(1).toLowerCase()
  );
  return capital.join("");
}

function removeScope(str) {
  let arr = str.split("/");
  if (arr.length > 1) {
    return arr[1];
  }
  return str;
}

let packageName = removeScope(process.env.npm_package_name);

let packageExportName = camelize(packageName);

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), injectExternals(CDNSettings)],
  optimizeDeps: {
    include: [
      "@incomnetworking/pagebuilder-ckeditor",
      "@incomnetworking/vue-component-ckeditor",
      "@incomnetworking/pagebuilder-ckeditor",
      "@incomnetworking/ckeditor5-vue",
      "@incomnetworking/vue-component-multilang-editor",
    ],
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
      "#": fileURLToPath(new URL("./node_modules", import.meta.url)),
    },
  },
  build: {
    lib: {
      entry: "./src/index.js",
      name: packageExportName,
      // the proper extensions will be added
      fileName: packageName,
      formats: ["umd"],
    },
    rollupOptions: {
      // make sure to externalize deps that shouldn't be bundled
      // into your library
      external: ["vue", "@incomnetworking/pagebuilder-ckeditor"],
      output: {
        // Provide global variables to use in the UMD build
        // for externalized deps
        globals: {
          vue: "Vue",
          "@incomnetworking/pagebuilder-ckeditor": "ClassicEditor",
        },
      },
    },
  },
  server: {
    port: 3001,
  }
});
