<style lang="scss">
.sort-wrapper {
  border: 1px dashed var(--input-border-color);
  width: max-content;

  ul {
    padding-left: 1rem;
    list-style: none;
  }
}
</style>
<style lang="css" scoped>
.preview {
  width: 300px;
}
</style>
<template>
  <div class="content-list">
    <div
      class="content-list-item d-md-flex align-items-center justify-content-between gap-md-3"
    >
      <div>
        <div class="d-flex align-items-center justify-content-between flex-row">
          <div>
            <div class="content-name ms-1">
              {{ item.title }}
            </div>
            <div v-if="isAttachment" class="preview">
              <img :src="item.publicUrl" />
            </div>
            <div
              class="item-description ms-1"
              v-html="keepNewLine(item.description)"
            ></div>
          </div>
          <FieldAdd
            :category="item"
            :item="isEditField"
            :isAttachment="isAttachment"
            v-if="isAdd || isEditField !== false"
            @cancelModal="cancelModalAdd"
            @added="fieldAdded"
            @edited="fieldEdited"
            @delete="fieldToDelete"
          />
          <FieldsSelectView
            :category="item"
            :isAttachment="isAttachment"
            v-if="isAddExists !== false"
            @added="fieldAddedSelected"
            @cancelModal="cancelModalAdd"
          />
          <FieldDelete
            :item="toDelete"
            :category="item.id"
            v-if="toDelete !== false"
            @cancelModal="cancelModalAdd"
            @deleted="fieldDeleted"
            @edited="fieldDeleted"
          />
        </div>
        <div v-if="isSort" class="sort-wrapper ms-1 mt-2 pt-1 pe-4 pb-3">
          <draggable
            v-model="sortFields"
            handle=".handle"
            tag="ul"
            class="dragArea"
            :item-key="getKey"
            :group="{ name: 'group-fields' }"
          >
            <template #item="{ element, index }">
              <li class="menu-row mt-3">
                <div
                  class="d-flex align-items-center justify-content-left flex-row"
                >
                  <div class="handle">
                    <i class="fa-solid fa-up-down-left-right"></i>
                  </div>
                  <div class="ms-2">
                    <span
                      class="badge rounded-pill text-bg-info shadow-sm"
                      @click="isEditField = element"
                    >
                      <span
                        v-if="element.icon"
                        class="me-2"
                        v-html="element.icon"
                      />
                      {{ $t(element.title) }}
                      <span class="ms-2 text-secondary"
                        ><i class="fa-solid fa-pen"></i>
                      </span>
                    </span>
                  </div>
                </div>
              </li>
            </template>
          </draggable>
          <div>
            <button class="btn btn-success ms-3" @click="saveOrder">
              {{ $t("Save") }}
            </button>
            <button class="btn btn-secondary m-2" @click="resetSort">
              {{ $t("Cancel") }}
            </button>
          </div>
        </div>
        <div class="d-flex flex-wrap gap-2 pt-2" v-if="!isSort">
          <LoadingView v-if="isLoading" class="text-secondary ms-5" />
          <div v-for="field in sortedFields" :key="field.id">
            <span
              class="badge rounded-pill text-bg-info shadow-sm"
              @click="isEditField = field"
              v-h-tooltip.html="itemDescription(field)"
            >
              <span v-if="field.icon" v-html="field.icon" />
              {{ $t(field.title) }}
              <span class="text-secondary"
                ><i class="fa-solid fa-pen"></i>
              </span>
            </span>
          </div>
        </div>
        <div
          class="d-flex align-items-center justify-content-left flex-row mt-2"
        >
          <div class="text-end time ms-2">
            <TimeView
              :timestamp="item.changed"
              :absolute="absolute"
              @set-absolute="setAbsolute"
            />
          </div>
          <div
            class="badge rounded-pill text-bg-info shadow-sm ms-2"
            v-for="searchType of item.searchType"
            :key="searchType"
          >
            {{ searchTypeOptions[searchType] }}
          </div>
        </div>
      </div>
      <div class="actions justify-content-md-end d-flex">
        <div class="item">
          <a
            class="link"
            href=""
            @click.prevent="isSort = !isSort"
            v-b-tooltip.hover.top
            title="Sort"
          >
            <i class="fa-solid fa-shuffle"></i>
          </a>
        </div>
        <div class="item">
          <a
            class="link"
            href=""
            @click.prevent="isAdd = true"
            v-b-tooltip.hover.top
            title="Add"
          >
            <i class="fa-sharp fa-solid fa-plus"></i>
          </a>
        </div>
        <div class="item">
          <a
            class="link"
            href=""
            @click.prevent="isAddExists = true"
            v-b-tooltip.hover.top
            title="Select Item"
          >
            <i class="fa-solid fa-clone"></i>
          </a>
        </div>
        <div class="item">
          <a
            class="link"
            :href="'#edit/' + item.id"
            v-b-tooltip.hover.top
            title="Edit"
          >
            <i class="fa-sharp fa-solid fa-pencil"></i>
          </a>
        </div>
        <div class="item">
          <a
            class="link"
            :href="'#delete/' + item.id"
            v-b-tooltip.hover.top
            title="Delete"
          >
            <i class="fa-sharp fa-solid fa-trash"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import TimeView from "@incomnetworking/vue-component-time-view";
import FieldAdd from "./ItemAdd.vue";
import FieldDelete from "./ItemDelete.vue";
import FieldsSelectView from "./FieldsSelectView.vue";
import draggable from "vuedraggable";
import DirectiveTooltip from "@incomnetworking/vue-directive-tooltip";
import LoadingView from "./LoadingView.vue";
export default {
  // Properties returned from data() become reactive state
  // and will be exposed on `this`.
  data: function () {
    return {
      isSort: false,
      isAdd: false,
      isAddExists: false,
      isEditField: false,
      toDelete: false,
      absolute: false,
      sortFields: false,
      fields: [],
      isLoading: true,
    };
  },
  directives: {
    "h-tooltip": DirectiveTooltip,
  },
  props: ["item", "isAttachment"],
  emits: ["field-added", "field-edited", "field-deleted", "sort-updated"],
  components: {
    TimeView,
    FieldAdd,
    FieldDelete,
    FieldsSelectView,
    draggable,
    LoadingView,
  },
  watch: {
    isSort: function (newValue) {
      if (newValue) {
        this.sortFields = JSON.parse(JSON.stringify(this.sortedFields));
      } else {
        this.sortFields = false;
      }
    },
    absolute: function (newValue) {
      if (this.$state.isRelative !== undefined) {
        this.$state.isRelative = !newValue;
      }
    },
    "$state.isRelative": function (newValue) {
      this.absolute = !newValue;
    },
  },
  computed: {
    sortedFields: function () {
      var fieldGroupFields = this.item.fields;
      if (fieldGroupFields === undefined) {
        fieldGroupFields = {};
      }
      return this.fields.sort(function (a, b) {
        var aOrder = 100;
        var bOrder = 100;
        if (fieldGroupFields[a.name] !== undefined) {
          aOrder = fieldGroupFields[a.name];
        }

        if (fieldGroupFields[b.name] !== undefined) {
          bOrder = fieldGroupFields[b.name];
        }

        if (aOrder < bOrder) {
          return -1;
        } else if (aOrder > bOrder) {
          return 1;
        }
        // a must be equal to b
        return 0;
      });
    },
    searchTypeOptions: function () {
      return {
        residential: this.$interface.getText(
          "search-type-residential",
          "Residential"
        ),
        commercial: this.$interface.getText(
          "search-type-commercial",
          "Commercial"
        ),
        condominium: this.$interface.getText(
          "search-type-condominium",
          "Condominium"
        ),
        "luxury-residential": this.$interface.getText(
          "search-type-luxury-residential",
          "Luxury Residential"
        ),
      };
    },
  },
  methods: {
    loadFields: function () {

      var query = {
        category: this.item.id,
        component: {
          $in: ["vue-component-category-items-test"],
        },
      };
      this.$api.getCategories(query).then((response) => {
        this.$debug.info("getCategories", query, response);
        this.isLoading = false;
        if (response.error) {
          this.$debug.error("getCategories:error", response);
          return;
        }
        this.fields = [];
        for (var field of response.answer) {
          this.fields.push(field);
        }
      });
    },
    itemDescription: function (item) {
      if (item.description && item.description != "") {
        return this.$t(item.description);
      }
      return false;
    },
    saveOrder: function () {
      var order = 0;
      if (this.item.fields === undefined) {
        this.item.fields = {};
      }
      for (var item of this.sortFields) {
        this.item.fields[item.name] = order;
        order++;
      }
      this.updateCategory();
      this.isSort = false;
    },
    updateCategory: function () {
      var update = {
        fields: this.item.fields,
      };
      this.$api.updateCategory(this.item.id, update).then((response) => {
        this.$debug.info("updateCategory", update, response);
        if (response.error) {
          return this.$debug.log("updateCategory", response.error);
        }
        this.$emit("edited", response.answer);
      });
    },
    resetSort: function () {
      this.sortFields = JSON.parse(JSON.stringify(this.fields));
      this.isSort = false;
    },
    getKey: function (element, index) {
      console.log("getKey", element, index);
      return element.name;
    },
    keepNewLine: function (value) {
      return this.$t(value).replace(/(?:\r\n|\r|\n)/g, "<br>");
    },
    cancelModalAdd: function () {
      this.isAddExists = false;
      this.isEditField = false;
      this.toDelete = false;
      this.isAdd = false;
    },
    fieldEdited: function (item) {
      this.isEditField = false;
      //this.$emit("field-edited", item);
      this.loadFields();
    },
    fieldAdded: function (item) {
      console.log("isAddExists:fieldAdded", item);
      this.isAddExists = false;
      this.isEditField = false;
      //this.$emit("field-added", item);
      this.loadFields();
    },
    fieldAddedSelected: function (item) {
      console.log("isAddExists:fieldAdded", item);
      //this.$emit("field-added", item);
      this.loadFields();
    },
    fieldToDelete: function (item) {
      this.toDelete = item;
      this.isAdd = false;
      this.isEditField = false;
    },
    fieldDeleted: function (item) {
      this.toDelete = false;
      this.isAdd = false;
      this.isEditField = false;
      //this.$emit("field-deleted", item);
      this.loadFields();
    },
    setAbsolute: function (absolute) {
      this.absolute = absolute;
    },
  },
  mounted: function () {
    this.loadFields();
  },
};
</script>
