export default {
  oneColumn: {
    image:
      "data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='752pt' height='752pt' version='1.1' viewBox='0 0 752 752' xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Cpath d='m270.05 170.05h202.26v411.91h-202.26z'/%3E%3C/g%3E%3C/svg%3E%0A",
    settings: {
      name: "One column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [{ name: "1", type: "col" }],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  twoColumn: {
    image:
      "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNzUycHQiIGhlaWdodD0iNzUycHQiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDc1MiA3NTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8cGF0aCBkPSJtMTcwLjA1IDE3MC4wNWgyMDIuMjZ2NDExLjkxaC0yMDIuMjZ6Ii8+CiAgPHBhdGggZD0ibTM3OS43IDE3MC4wNWgyMDIuMjZ2NDExLjkxaC0yMDIuMjZ6Ii8+CiA8L2c+Cjwvc3ZnPgo=",
    settings: {
      name: "Two column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [
        { name: "1", type: "col" },
        { name: "2", type: "col" },
      ],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  threeColumn: {
    image:
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAMAAAAJbSJIAAAAG1BMVEUAAAD///+QkJDX19eCgoKMjIzq6uq9vb19fX104piDAAABjUlEQVR4nO3PwW3DUBBDQVm2LPVfca7KkfhJmDVmCiD4tsen29oHfp3C+RTOp3A+hfMpnE/hfArnUzifwvkUzqdwPoXzbfuq433fO4/VufM+916d2/dt3fN+6Vqeu+5zzx/4t+5b4Wt57qXw7ykMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIb+e+G1PHf9dOG+6njfL53H6tx5n3uvzu379vh0CudTOJ/C+RTOp3A+hfMpnE/hfArnUzifwvkUzvf5hV9/Gkq3U+bouQAAAABJRU5ErkJggg==",
    settings: {
      name: "Three column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [
        { name: "1", type: "col" },
        { name: "2", type: "col" },
        { name: "3", type: "col" },
      ],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  aoneColumn: {
    image:
      "data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='752pt' height='752pt' version='1.1' viewBox='0 0 752 752' xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Cpath d='m270.05 170.05h202.26v411.91h-202.26z'/%3E%3C/g%3E%3C/svg%3E%0A",
    settings: {
      name: "One column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [{ name: "1", type: "col" }],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  atwoColumn: {
    image:
      "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNzUycHQiIGhlaWdodD0iNzUycHQiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDc1MiA3NTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8cGF0aCBkPSJtMTcwLjA1IDE3MC4wNWgyMDIuMjZ2NDExLjkxaC0yMDIuMjZ6Ii8+CiAgPHBhdGggZD0ibTM3OS43IDE3MC4wNWgyMDIuMjZ2NDExLjkxaC0yMDIuMjZ6Ii8+CiA8L2c+Cjwvc3ZnPgo=",
    settings: {
      name: "Two column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [
        { name: "1", type: "col" },
        { name: "2", type: "col" },
      ],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  athreeColumn: {
    image:
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAMAAAAJbSJIAAAAG1BMVEUAAAD///+QkJDX19eCgoKMjIzq6uq9vb19fX104piDAAABjUlEQVR4nO3PwW3DUBBDQVm2LPVfca7KkfhJmDVmCiD4tsen29oHfp3C+RTOp3A+hfMpnE/hfArnUzifwvkUzqdwPoXzbfuq433fO4/VufM+916d2/dt3fN+6Vqeu+5zzx/4t+5b4Wt57qXw7ykMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIb+e+G1PHf9dOG+6njfL53H6tx5n3uvzu379vh0CudTOJ/C+RTOp3A+hfMpnE/hfArnUzifwvkUzvf5hV9/Gkq3U+bouQAAAABJRU5ErkJggg==",
    settings: {
      name: "Three column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [
        { name: "1", type: "col" },
        { name: "2", type: "col" },
        { name: "3", type: "col" },
      ],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  boneColumn: {
    image:
      "data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='752pt' height='752pt' version='1.1' viewBox='0 0 752 752' xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Cpath d='m270.05 170.05h202.26v411.91h-202.26z'/%3E%3C/g%3E%3C/svg%3E%0A",
    settings: {
      name: "One column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [{ name: "1", type: "col" }],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  btwoColumn: {
    image:
      "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iNzUycHQiIGhlaWdodD0iNzUycHQiIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDc1MiA3NTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8cGF0aCBkPSJtMTcwLjA1IDE3MC4wNWgyMDIuMjZ2NDExLjkxaC0yMDIuMjZ6Ii8+CiAgPHBhdGggZD0ibTM3OS43IDE3MC4wNWgyMDIuMjZ2NDExLjkxaC0yMDIuMjZ6Ii8+CiA8L2c+Cjwvc3ZnPgo=",
    settings: {
      name: "Two column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [
        { name: "1", type: "col" },
        { name: "2", type: "col" },
      ],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
  bthreeColumn: {
    image:
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAMAAAAJbSJIAAAAG1BMVEUAAAD///+QkJDX19eCgoKMjIzq6uq9vb19fX104piDAAABjUlEQVR4nO3PwW3DUBBDQVm2LPVfca7KkfhJmDVmCiD4tsen29oHfp3C+RTOp3A+hfMpnE/hfArnUzifwvkUzqdwPoXzbfuq433fO4/VufM+916d2/dt3fN+6Vqeu+5zzx/4t+5b4Wt57qXw7ykMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIYUFigMKSxQGFJYoDCksEBhSGGBwpDCAoUhhQUKQwoLFIb+e+G1PHf9dOG+6njfL53H6tx5n3uvzu379vh0CudTOJ/C+RTOp3A+hfMpnE/hfArnUzifwvkUzvf5hV9/Gkq3U+bouQAAAABJRU5ErkJggg==",
    settings: {
      name: "Three column section",
      description: "Flex all width column",
      component: "vue-component-section",
      columns: [
        { name: "1", type: "col" },
        { name: "2", type: "col" },
        { name: "3", type: "col" },
      ],
      widgets: [],
    },
    state: {
      hide: {
        mobile: false,
        tablet: false,
        desktop: false,
      },
      css: {
        id: "",
        classes: "",
      },
    },
  },
};
