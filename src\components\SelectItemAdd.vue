<style lang="scss" scoped></style>
<template>
  <Teleport to="body">
    <ModalView
      ref="modal"
      :title="$interface.getText('add-field-modal-title', 'New Select Item')"
      :show-title="true"
      @hiddenBsModal="$emit('cancelModal')"
      :show-on-mount="true"
      class="modal-dialog-centered modal-lg"
      :actions="actions"
    >
      <div class="row pt-2">
        <div class="col">
          <FormInput
            v-model="data.title"
            :label="$interface.getText('field-items-title', 'Title')"
          />
        </div>
      </div>
      <div class="row pt-2" v-if="data !== false">
        <div class="col">
          <FormInput
            v-model="data.value"
            :label="$interface.getText('field-items-value', 'Machine Value')"
            :describe="$t('Machine friendly value')"
          />
        </div>
      </div>
      <div class="row pt-2">
        <div class="col">
          <FormInput
            v-model="data.description"
            type="textarea"
            :cols="20"
            :label="$interface.getText('field-item-description', 'Description')"
          ></FormInput>
        </div>
      </div>
      <div class="row pt-2">
        <div class="col">
          <FormInput
            v-model="data.icon"
            :label="$interface.getText('menu-icon', 'Icon')"
          />
        </div>
      </div>
    </ModalView>
  </Teleport>
</template>
<script>
import {
  FormInput,
  FormCheckbox,
  FormSelect,
  FormRange,
} from "@incomnetworking/vue-form-components";
import ModalView from "@incomnetworking/vue-bootstrap-modal";
import machineName from "../machine.js";
export default {
  data() {
    return {
      data: false,
    };
  },
  components: {
    FormInput,
    FormCheckbox,
    FormSelect,
    FormRange,
    ModalView,
  },
  emits: ["cancelModal", "SelectItemAdded"],
  props: ["item", "type"],
  computed: {
    actions: function () {
      var disabled = undefined;
      if (this.data) {
        if (this.data.value == "") {
          disabled = true;
        }
        if (this.data.title == "") {
          disabled = true;
        }
      }
      var actions = [];
      actions.push({
        title: this.$interface.getText("btn-modal-cancel", "Cancel"),
        class: "btn-secondary",
        click: () => {
          this.$refs["modal"].hide(false);
          this.$refs["modal"].showPrevious();
          return false;
        },
      });
      if (this.item === undefined) {
        actions.push({
          title: this.$interface.getText("btn-select-item-add", "Add"),
          class: "btn-success",
          disabled: disabled,
          click: () => {
            this.createMenuItem();
            return false;
          },
        });
      } else {
        actions.push({
          title: this.$interface.getText("btn-select-item-edit", "Save"),
          class: "btn-success",
          disabled: disabled,
          click: () => {
            this.createMenuItem();
            return false;
          },
        });
      }
      return actions;
    },
  },
  methods: {
    createMenuItem: function () {
      if (this.type == "selectNumber") {
        this.data.value = parseFloat(this.data.value);
      }
      this.$emit("SelectItemAdded", this.data);
      this.$refs["modal"].hide(false);
      this.$refs["modal"].showPrevious();
    },
  },
  watch: {
    items: function (newValue) {
      this.data = JSON.parse(JSON.stringify(newValue));
    },
    data: function (newValue) {
      console.log("data", newValue);
    },
    "data.title": function (newValue) {
      this.data.value = machineName(newValue);
    },
  },
  mounted: function () {
    if (this.item !== undefined) {
      this.data = JSON.parse(JSON.stringify(this.item));
    } else {
      this.data = {
        value: "",
        title: "",
        description: "",
      };
    }
    this.$api.setVariables("userId", this.$auth.User.id);
  },
};
</script>
