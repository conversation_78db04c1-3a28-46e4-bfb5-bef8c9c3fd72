<style scoped>
.machine-name {
  display: none;
}
</style>

<template>
  <ModalView
    ref="modal"
    :title="title"
    :show-title="true"
    @hiddenBsModal="hiddenModal"
    :show-on-mount="true"
    class="modal-dialog-centered modal-md add-category"
    :actions="[
      {
        title: this.$interface.getText('btn-modal-cancel', 'Cancel'),
        class: 'btn-secondary',
        click: function () {
          cancelAdd();
        },
      },
      {
        title: this.$interface.getText('btn-modal-save', 'Save'),
        class: 'btn-success',
        click: function () {
          save();
          return true;
        },
      },
    ]"
  >
    <div class="row row-cols-1 row-cols-md-2">
      <div class="col-md-12 form-input-wrapper required">
        <FormInput
          ref="categoryName"
          v-model="data.title"
          :describe="$t('User will see this name on category selection')"
          :label="$interface.getText('category-name', 'Category Name')"
          :validation="validateCategoryName"
        />
        <div class="form-text machine-name" v-if="$auth.isAdmin">
          {{ "Machine name: " + data.name }}
        </div>
      </div>
      <div class="col-md-12 form-thumbnail-input-wrapper mt-3">
        <FileSingleUpload
          :label="$t('category-attached-file', 'Attached File')"
          v-model="data.fileId"
          accept="image/*"
          :temporary="false"
          api-post="postUserFiles"
          :allow-delete="true"
          :multiple="false"
          :validation="fileUploadValidation"
          ref="attachedFile"
        />
        <div
          v-if="!data.fileId"
          @click.prevent="attachFileClick"
          class="thumbnail-image-preview border p-2 border-dashed rounded d-flex align-items-center justify-content-center w-100 position-relative"
          :class="{ disabled: data.fileId }"
        >
          <div class="text-center">
            <i class="image-icon fa-solid fa-file"></i>
            <div class="input-instruction-text">
              <span>{{
                $t(
                  "content-marketing-add-data-subtext",
                  "Click to upload file"
                )
              }}</span>
              <span class="ext">{{
                $t(
                  "content-marketing-add-data-file-size",
                  "Files must be less than 50 MB"
                )
              }}</span>
              <span class="ext">{{
                $t(
                  "content-marketing-add-data-file-type",
                  "Allowed file types: png jpg jpeg gif bmp webp"
                )
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <!--div class="col-md-12 form-item" v-if="$auth.isAdmin">
        <FormInput
          v-model="data.description"
          type="textarea"
          cols="40"
          :describe="$t('User will see this name on property creating')"
          :label="
            $interface.getText(
              'category-description',
              'Category description'
            )
          "
        />
      </!--div-->
      <!--div class="col-md-12 form-item">
        <FormInput
          v-model="data.order"
          :describe="$t('Group rendering order')"
          :label="
            $interface.getText(
              'category-order',
              'Order for the group'
            )
          "
        />
      </!--div-->
    </div>
    <div class="mb-3"></div>
  </ModalView>
</template>
<script>
import {
  FormInput,
  FormCheckbox,
  FormSelect,
  FormRange,
} from "@incomnetworking/vue-form-components";
import ModalView from "@incomnetworking/vue-bootstrap-modal";
import DropdownComponent from "@incomnetworking/vue-bootstrap-dropdown";

import machineName from "../machine.js";
import {
  FileSingleUpload,
  getFile,
} from "@incomnetworking/vue-component-gcloud-files";

export default {
  data() {
    return {
      data: {
        name: "",
        title: "",
        description: "",
        order: 0,
        fileId: false,
        searchType: [],
        component: "vue-component-category",
      },
      validate: {
        name: false,
      },
    };
  },
  components: {
    FormInput,
    FormCheckbox,
    FormSelect,
    FormRange,
    ModalView,
    DropdownComponent,
    FileSingleUpload,
  },
  props: ["item", "isAttachment"],
  emits: ["cancelModal", "added", "edited"],
  watch: {
    "data.fileId": function (fileId) {
      if (fileId === false) {
        this.data.publicUrl = false;
        return;
      }
      this.getFile(fileId).then((file) => {
        if (file.publicUrl) {
          this.data.publicUrl = file.publicUrl;
        }
      });
    },
    "data.title": function (newValue) {
      this.data.name = machineName(newValue);
    },
  },
  computed: {
    title: function () {
      if (this.item) {
        return this.$interface.getText(
          "edit-category-modal-title",
          "Edit category"
        );
      }
      return this.$interface.getText(
        "add-category-modal-title",
        "New Category"
      );
    },
  },
  methods: {
    getFile: getFile,
    fileUploadValidation: function (file, callback) {
      // 50 mb size limit
      if (file.size > 50 * 1024 * 1024) {
        callback({ valid: false, message: "File is too large" });
        return;
      }
      const allowedExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      const fileExtension = file.name.split(".").pop().toLowerCase();
      if (!allowedExtensions.includes(fileExtension)) {
        callback({
          valid: false,
          message:
            "Not allowed file extension. Allowed only " +
            allowedExtensions.join(", "),
        });
        return;
      }

      callback({ valid: true, message: "Uploaded" });
    },
    eventShowModal: function () {
      this.isHidden = true;
    },
    hiddenModal: function () {
      return this.$emit("cancelModal");
    },
    showModal: function () {
      this.$refs["modal"].show();
    },
    cancelAdd: function () {
      this.$emit("cancelModal");
    },
    save: function () {
      if (!this.validate.name) {
        this.$refs["categoryName"].Validate();
        return;
      }
      if (this.item) {
        return this.updateCategory();
      }
      this.createCategory();
    },
    updateCategory: function () {
      var update = {};
      Object.keys(this.data).forEach((key) => {
        if (key == "ownerIds") {
          return;
        }
        if (key == "order") {
          update[key] = parseFloat(this.data[key]);
          return;
        }
        if (this.data[key] !== this.item[key]) {
          update[key] = this.data[key];
        }
      });
      this.$api.updateCategory(this.item.id, update).then((response) => {
        this.$debug.info("updateCategory", update, response);
        if (response.error) {
          return this.$debug.log("updateCategory", response.error);
        }
        this.cancelAdd();
        this.$emit("edited", response.answer);
      });
    },
    createCategory: function () {
      this.$api.postCategory(this.data).then((response) => {
        this.$debug.info("postCategory", this.data, response);
        if (response.error) {
          return this.$debug.error("postCategory", this.data, response);
        }
        this.cancelAdd();
        this.$emit("added", response.answer);
      });
    },
    validateCategoryName(value, callback) {
      if (!value || value.trim() === "") {
        this.validate.name = false;
        callback({
          valid: false,
          message: "Category Name is manadatory field",
        });
      } else {
        this.validate.name = true;
        callback({
          valid: true,
          message: "",
        });
      }
    },
    attachFileClick: function () {
      if (!this.data.fileId) {
        this.$refs.attachedFile.uploadClick();
      }
    },
  },
  mounted: function () {
    if (this.item) {
      this.data = JSON.parse(JSON.stringify(this.item));
    }
    this.$api.setVariables("userId", this.$auth.User.id);
  },
};
</script>
 