<style scoped>
.machine-name {
  display: none;
}
</style>

<template>
  <Teleport to="body">
    <ModalView
      ref="itemAddModal"
      :title="title"
      :show-title="true"
      @hiddenBsModal="hiddenModal"
      :show-on-mount="true"
      class="modal-dialog-centered modal-md add-item"
      :actions="actions"
    >
      <div class="row row-cols-1 row-cols-md-2">
        <div class="col-md-12 form-input-wrapper required">
          <FormInput
            ref="itemName"
            v-model="data.title"
            :describe="$t('User will see this name on item selection')"
            :label="$interface.getText('category-item-name', 'Item Name')"
            :validation="validateItemName"
          />
          <div class="form-text machine-name" v-if="$auth.isAdmin">
            {{ "Machine name: " + data.name }}
          </div>
        </div>
        <div class="col-md-12 form-thumbnail-input-wrapper mt-3">
          <FileSingleUpload
            :label="$t('category-item-attached-file', 'Attached File')"
            v-model="data.fileId"
            accept="image/*"
            :temporary="false"
            api-post="postUserFiles"
            :allow-delete="true"
            :multiple="false"
            :validation="fileUploadValidation"
            ref="attachedItemFile"
          />
          <div
            v-if="!data.fileId"
            @click.prevent="attachFileClick"
            class="thumbnail-image-preview border p-2 border-dashed rounded d-flex align-items-center justify-content-center w-100 position-relative"
            :class="{ disabled: data.fileId }"
          >
            <div class="text-center">
              <i class="image-icon fa-solid fa-file"></i>
              <div class="input-instruction-text">
                <span>{{
                  $t(
                    "content-marketing-add-data-subtext",
                    "Click to upload file"
                  )
                }}</span>
                <span class="ext">{{
                  $t(
                    "content-marketing-add-data-file-size",
                    "Files must be less than 50 MB"
                  )
                }}</span>
                <span class="ext">{{
                  $t(
                    "content-marketing-add-data-file-type",
                    "Allowed file types: png jpg jpeg gif bmp webp"
                  )
                }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="col-md-12 form-item">
          <span class="me-2 text-info" v-if="data.icon" v-html="data.icon" />
          <FormInput
            v-model="data.icon"
            :describe="
              $t(
                'If icon provided, it will be generated next to the name on property view'
              )
            "
            :label="$interface.getText('category-item-icon', 'Item Icon')"
          />
        </div>
        <div class="col-md-12 form-item">
          <FormInput
            v-model="data.description"
            type="textarea"
            cols="40"
            :describe="$t('User will see this name on property creating')"
            :label="
              $interface.getText(
                'category-item-description',
                'Item description'
              )
            "
          />
        </div> -->
      </div>
      <div class="mb-3"></div>
      <CollapsePanel
        :title="$interface.getText('menu-title', 'Items')"
        :show-on-mount="true"
        v-if="isSelectValue"
      >
        <SelectItems v-model="data.items" :type="data.type" />
      </CollapsePanel>
    </ModalView>
  </Teleport>
</template>
<script>
import {
  FormInput,
  FormCheckbox,
  FormSelect,
  FormRange,
} from "@incomnetworking/vue-form-components";
import ModalView from "@incomnetworking/vue-bootstrap-modal";
import CollapsePanel from "@incomnetworking/vue-component-collapse-panel";
import SelectItems from "./SelectItemsDraggable.vue";

import machineName from "../machine.js";
import {
  FileSingleUpload,
  getFile,
} from "@incomnetworking/vue-component-gcloud-files";

export default {
  data() {
    return {
      data: {
        name: "",
        title: "",
        icon: "",
        fileId: false,
        description: "",
        category: [],
        component: "vue-component-category-items",
      },
      validate: {
        name: false,
      },
    };
  },
  components: {
    FormInput,
    FormCheckbox,
    FormSelect,
    FormRange,
    ModalView,
    SelectItems,
    CollapsePanel,
    FileSingleUpload,
  },
  props: ["item", "category", "isAttachment"],
  emits: ["cancelModal", "added", "edited", "delete"],
  watch: {
    "data.fileId": function (fileId) {
      if (fileId === false) {
        this.data.publicUrl = false;
        return;
      }
      this.getFile(fileId).then((file) => {
        if (file.publicUrl) {
          this.data.publicUrl = file.publicUrl;
        }
      });
    },
    "data.title": function (newValue) {
      if (this.item) {
        // no updating machine name on edit
        return;
      }
      this.data.name = machineName(newValue);
    },
  },
  computed: {
    title: function () {
      if (this.item) {
        return this.$interface.getText("edit-field-modal-title", "Edit Field");
      }
      return this.$interface.getText("add-field-modal-title", "New Field");
    },
    actions: function () {
      var actions = [];
      var self = this;
      if (this.item) {
        actions.push({
          title: this.$interface.getText("btn-modal-delete", "Delete"),
          class: "btn-danger",
          click: function () {
            self.$emit("cancelModal", self.item);
            self.$emit("delete", self.item);
            return true;
          },
        });
      }
      // actions.push({
      //   title: this.$interface.getText("btn-modal-cancel", "Cancel"),
      //   class: "btn-secondary",
      //   click: function () {
      //     self.cancelAdd();
      //   },
      // });
      actions.push({
        title: this.$interface.getText("btn-modal-save", "Save"),
        class: "btn-success",
        click: function () {
          self.save();
          return true;
        },
      });
      return actions;
    },
    isSelectValue: function () {
      if (["selectNumber", "selectString"].indexOf(this.data.type) !== -1) {
        return true;
      }
      return false;
    },
    isSystemCategory: function () {
      // return this.category?.name === "categories" && this.category?.component === "vue-component-category";
      return this.category?.name === "test" && this.category?.component === "vue-component-category-test"; // for testing
    },
  },
  methods: {
    getFile: getFile,
    fileUploadValidation: function (file, callback) {
      // 50 mb size limit
      if (file.size > 50 * 1024 * 1024) {
        callback({ valid: false, message: "File is too large" });
        return;
      }
      const allowedExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      const fileExtension = file.name.split(".").pop().toLowerCase();
      if (!allowedExtensions.includes(fileExtension)) {
        callback({
          valid: false,
          message:
            "Not allowed file extension. Allowed only " +
            allowedExtensions.join(", "),
        });
        return;
      }

      callback({ valid: true, message: "Uploaded" });
    },
    eventShowModal: function () {
      this.isHidden = true;
    },
    hiddenModal: function () {
      return this.$emit("cancelModal");
    },
    showModal: function () {
      this.$refs["modal"].show();
    },
    cancelAdd: function () {
      this.$emit("cancelModal");
    },
    save: function () {
      if (!this.validate.name) {
        this.$refs["itemName"].Validate();
        return;
      }
      if (this.item) {
        return this.updateCategoryItem();
      }
      this.createCategoryItem();
    },
    updateCategoryItem: function () {
      var update = {
        $set: {},
      };
      Object.keys(this.data).forEach((key) => {
        if (key == "ownerIds") {
          return;
        }
        if (key == "category") {
          return;
        }
        if (this.data[key] !== this.item[key]) {
          update["$set"][key] = this.data[key];
        }
      });
      console.log("updateCategory", this.category);
      if (
        this.category &&
        this.data.category.indexOf(this.category.id) === -1
      ) {
        update["$push"] = {
          category: this.category.id,
        };
      }
      this.$api.updateCategory(this.item.id, update).then(async (response) => {
        this.$debug.info("updateCategory", update, response);
        if (response.error) {
          return this.$debug.log("updateCategory", response.error);
        }
        // Get separate entity and update their title
        const updatedTitle = update.$set?.title
        if (this.isSystemCategory && updatedTitle) {
          const { error, answer = [] } = await this.$api.getCategories({
            name: this.data.name,
            component: "vue-component-category-test"
          });

          const category = answer[0];
          if (!error && category?.id && category.title !== updatedTitle) {
            await this.$api.updateCategory(category.id, { $set: { title: updatedTitle } });
          }
        }
        this.cancelAdd();
        this.$emit("edited", response.answer);
      });
    },
    createCategoryItem: function () {
      if (this.category) {
        this.data.category.push(this.category.id);
      }
      this.$api.postCategory(this.data).then((response) => {
        this.$debug.info("postCategory", this.data, response);
        if (response.error) {
          return this.$debug.error("postCategory", this.data, response);
        }
        this.cancelAdd();
        this.$emit("added", response.answer);
      });
    },
    validateItemName: async function (value, callback) {
      if (!value || value.trim() === "") {
        this.validate.name = false;
        callback({
          valid: false,
          message: "Item Name is manadatory field",
        });
      }
      else {
        const isDuplicate = await this.checkDuplicateName();
        if (isDuplicate) {
          this.validate.name = false;
          callback({
            valid: false,
            message: "Item Name already exists",
          });
        } else {
          this.validate.name = true;
          callback({
            valid: true,
            message: "",
          });
        }
      }
    },
    attachFileClick: function () {
      if (!this.data.fileId) {
        this.$refs.attachedItemFile.uploadClick();
      }
    },
    checkDuplicateName: async function () {
      // Skip check if editing item
      if (this.item) return false;

      const query = {
        name: this.data.name,
        component: "vue-component-category-items",
      };
      const response = await this.$api.getCategories(query);

      if (response.error || !response.answer?.length) return false;
      return true;
    },
  },
  mounted: function () {
    if (this.item) {
      this.data = JSON.parse(JSON.stringify(this.item));
    }
    this.$api.setVariables("userId", this.$auth.User.id);
  },
};
</script>
