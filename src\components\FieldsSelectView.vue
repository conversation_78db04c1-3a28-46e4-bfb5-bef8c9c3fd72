<style lang="scss" scoped></style>
<template>
  <Teleport to="body">
    <ModalView
      ref="modal"
      :title="$interface.getText('select-items-modal-title', 'Select Item')"
      :show-title="true"
      @hiddenBsModal="$emit('cancelModal')"
      :show-on-mount="true"
      class="modal-dialog-centered modal-lg"
      :actions="actions"
    >
      <FieldsView :category="category" @fieldEdited="fieldEdited" />
    </ModalView>
  </Teleport>
</template>
<script>
import ModalView from "@incomnetworking/vue-bootstrap-modal";
import FieldsView from "./ItemsView.vue";
export default {
  data() {
    return {};
  },
  components: {
    ModalView,
    FieldsView,
  },
  emits: ["cancelModal", "added"],
  props: ["category"],
  computed: {
    actions: function () {
      var actions = [];
      actions.push({
        title: this.$interface.getText("btn-modal-cancel", "Cancel"),
        class: "btn-secondary",
        click: () => {
          this.$emit("cancelModal");
        },
      });
      return actions;
    },
  },
  methods: {
    fieldEdited: function (item) {
      console.log("isAddExists:fieldEdited", item, this.category);
      var update = {};
      if (this.category && item.category.indexOf(this.category.id) === -1) {
        update["$push"] = {
          category: this.category.id,
        };
        this.$api.updateCategory(item.id, update).then((response) => {
          this.$debug.info("updateCategory", update, response);
          if (response.error) {
            return this.$debug.log("updateCategory", response.error);
          }
          this.$emit("added", response.answer);
        });
        return;
      }

      this.$emit("added", item);
    },
  },
};
</script>
