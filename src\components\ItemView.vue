<template>
  <div class="content-list">
    <div
      class="content-list-item d-md-flex align-items-center justify-content-between gap-md-3"
    >
      <div>
        <div class="d-flex align-items-center justify-content-between flex-row">
          <div>
            <div class="content-name ms-1">
              <span
                v-if="item.icon"
                class="me-2 text-info"
                v-html="item.icon"
              />
              {{ $t(item.title) }}
            </div>
            <div v-if="isAttachment" class="preview">
              <img :src="item.publicUrl" />
            </div>
            <div
              class="item-description ms-1"
              v-html="keepNewLine(item.description)"
            ></div>
          </div>
          <ItemAdd
            :category="category"
            :item="item"
            v-if="isEditField !== false"
            @cancelModal="cancelModalAdd"
            @edited="fieldEdited"
            @delete="fieldToDelete"
          />
          <ItemDelete
            :item="toDelete"
            :category="true"
            v-if="toDelete !== false"
            @cancelModal="cancelModalAdd"
            @deleted="ItemDeleted"
          />
        </div>
        <div class="d-flex flex-wrap gap-2 pt-2">
          <div
            class="badge rounded-pill disabled text-bg-info shadow-sm"
            v-for="category in categories"
            :key="category.id"
          >
            {{ $t(category.title) }}
          </div>
        </div>
        <div
          class="d-flex align-items-center justify-content-left flex-row mt-2"
        >
          <div class="text-end time ms-2">
            <TimeView
              :timestamp="item.changed"
              :absolute="absolute"
              @set-absolute="setAbsolute"
            />
          </div>
        </div>
      </div>
      <div class="actions justify-content-md-end d-flex">
        <div class="item" v-if="isSelect === true">
          <a
            class="link"
            href="#"
            @click.prevent="addExistsFieldToGroup"
            v-b-tooltip.hover.top
            title="Add"
          >
            <i class="fa-sharp fa-solid fa-plus"></i>
          </a>
        </div>
        <div class="item" v-if="!isSelect">
          <a class="link" href="#" @click.prevent="isEditField = item"
            ><i class="fa-sharp fa-solid fa-pencil"></i
          ></a>
        </div>
        <div class="item" v-if="!isSelect">
          <a class="link" href="#" @click.prevent="toDelete = item"
            ><i class="fa-sharp fa-solid fa-trash"></i
          ></a>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import TimeView from "@incomnetworking/vue-component-time-view";
import ItemAdd from "./ItemAdd.vue";
import ItemDelete from "./ItemDelete.vue";
import DirectiveTooltip from "@incomnetworking/vue-directive-tooltip";
export default {
  data: function () {
    return {
      isEditField: false,
      toDelete: false,
      absolute: false,
      categories: [],
    };
  },
  props: ["item", "category", "isSelect", "isAttachment"],
  emits: ["field-deleted", "field-edited"],
  components: {
    TimeView,
    ItemAdd,
    ItemDelete,
  },
  directives: {
    "b-tooltip": DirectiveTooltip,
  },
  watch: {
    absolute: function (newValue) {
      if (this.$state.isRelative !== undefined) {
        this.$state.isRelative = !newValue;
      }
    },
    "$state.isRelative": function (newValue) {
      this.absolute = !newValue;
    },
  },
  computed: {},
  methods: {
    cancelModalAdd: function () {
      this.isEditField = false;
      this.toDelete = false;
    },
    fieldToDelete: function (item) {
      this.toDelete = item;
      this.isEditField = false;
    },
    ItemDeleted: function (item) {
      this.toDelete = false;
      this.isEditField = false;
      this.$emit("field-deleted", item);
    },
    fieldEdited: function (item) {
      this.isEditField = false;
      this.$emit("field-edited", item);
    },
    addExistsFieldToGroup: function () {
      console.log("add field to group", this.item, this.category);
      this.$emit("field-edited", this.item);
    },
    itemDescription: function (item) {
      if (item.description && item.description != "") {
        return this.$t(item.description);
      }
      return false;
    },
    keepNewLine: function (value) {
      return this.$t(value).replace(/(?:\r\n|\r|\n)/g, "<br>");
    },
    setAbsolute: function (absolute) {
      this.absolute = absolute;
    },
    loadCategories: function () {
      var query = {
        id: { $in: this.item.category },
        component: "vue-component-category",
      };
      this.$api.getCategories(query).then((response) => {
        this.$debug.info("getCategories", query, response);
        this.isLoading = false;
        if (response.error) {
          this.$debug.error("getCategories:error", response);
          return;
        }
        for (var category of response.answer) {
          this.categories.push(category);
        }
      });
    },
  },
  mounted: function () {
    this.loadCategories();
  },
};
</script>
