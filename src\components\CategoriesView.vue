<style scoped>
  .filter-wrapper :deep(.filter .actions .clear-filter-button) {
    display: none;
  }
</style> 

<template>
  <CategoryAdd
    v-if="mode == 'edit' && menu != false"
    :item="menu"
    :isAttachment="isAttachment"
    @cancelModal="unSelectMenu"
    @edited="updateMenu"
  />
  <CategoryAdd
    v-if="mode == 'add'"
    :isAttachment="isAttachment"
    @cancelModal="unSelectMenu"
    @added="updateMenu"
  />
  <CategoryDelete
    v-if="mode == 'delete' && menu != false"
    :item="menu"
    @cancelModal="unSelectMenu"
    @deleted="updateMenu"
  />
  <div class="row fields-group-view">
    <div class="p-0 filter-wrapper">
      <FilterBar
        v-model="query"
        @clearTrigger="clearTrigger"
        name="admin-menu-filter"
        :settings="settings"
        class="content-filter container-fluid border border-bottom-0 border-info rounded-top"
        @clearFilter="clearFilter"
      >
      </FilterBar>
      <ItemsView
        class="stripped"
        :listings="Categories"
        :is-updating="isLoading"
        v-model:page="page"
        v-model:sort="sort"
        :sortOptions="sortOptions"
        sortLabel="Sort:"
        :total="total"
        :isHideMap="true"
        v-model:step="step"
        :stepOptions="stepOptions"
        row-class="row-cols-1"
        wrapper="container-fluid"
      >
        <template
          v-slot:[category.id]
          v-for="category in Categories"
          :key="category.id"
        >
          <CategoryView
            :item="category"
            :isAttachment="isAttachment"
            @fieldAdded="refresh"
            @fieldEdited="refresh"
            @fieldDeleted="refresh"
            @sortUpdated="refresh"
          />
        </template>
      </ItemsView>
    </div>
  </div>
</template>
<script>
import CategoryAdd from "./CategoryAdd.vue";
import CategoryView from "./CategoryView.vue";
import CategoryDelete from "./CategoryDelete.vue";
import ItemsView from "@incomnetworking/vue-component-items-list";
import FilterBar from "@incomnetworking/vue-component-filter-bar";

export default {
  // Properties returned from data() become reactive state
  // and will be exposed on `this`.
  data: function () {
    return {
      query: {
        component: {
          // $in: ["vue-component-category"],
          $in: ["vue-component-category-test"], // for testing only
        },
      },
      sort: {
        changed: -1,
      },
      latestQuerySTR: "",
      isLoading: true,
      refreshTimer: false,
      refreshDelayTimer: false,
      autocompleteSuggestions: [],
      isAutocompleteInFocus: false,
      addressSearchTimeout: false,
      menus: [],
      menu: false,
      skip: 0,
      page: 0,
      step: 24,
      total: 0,
    };
  },
  props: ["isAttachment"],

  components: {
    CategoryAdd,
    CategoryView,
    CategoryDelete,
    FilterBar,
    ItemsView,
  },

  watch: {
    sort: {
      handler: function (newValue) {
        this.refresh();
      },
      deep: true,
    },
    "query.searchText": function (searchText) {
      console.log("query.searchText", searchText);
      if (this.isAutocompleteInFocus) {
        this.searchAutocomplete(searchText);
      }
    },
    query: {
      handler: function () {
        var searchQuery = JSON.parse(JSON.stringify(this.query));
        // removing autocomplete element
        delete searchQuery.searchText;

        if (this.latestQuerySTR == JSON.stringify(searchQuery)) {
          this.$debug.log("nothing changed", this.latestQuerySTR);
          return;
        }
        this.latestQuerySTR = JSON.stringify(searchQuery);
        this.refresh();
      },
      deep: true,
    },
    skip: function () {
      this.refresh();
    },
    step: function () {
      this.page = 0;
      this.refresh();
    },
    page: function (newValue) {
      this.skip = newValue * this.step;
    },
    menuId: function (newValue) {
      if (newValue && newValue != "") {
        this.loadMenu(newValue);
      }
    },
  },
  computed: {
    Categories: function () {
      return this.menus;
    },
    searchTextFilters: function () {
      var fields = [
        {
          field: "title",
          title: this.$t("Title"),
        },
      ];
      return fields;
    },
    stepOptions: function () {
      var steps = [6, 24, 48, 96];
      var options = [];
      for (var i of steps) {
        options.push({
          value: i,
          title: i + " " + this.$interface.getText("per-page", "per page"),
        });
      }
      return options;
    },
    sortOptions: function () {
      var sorts = [];
      sorts.push({
        title: this.$interface.getText("sort-by-title-newest", "By Newest"),
        value: { changed: -1 },
      });
      sorts.push({
        title: this.$interface.getText("sort-by-title-oldest", "By Oldest"),
        value: { changed: 1 },
      });
      sorts.push({
        title: this.$interface.getText("sort-by-name", "Alphabetically"),
        value: {
          name: 1,
          title: 1,
        },
      });
      return sorts;
    },
    settings: function () {
      var settings = [];
      /* Autocomplete search box*/
      settings.push({
        type: "autocomplete",
        value: "searchText",
        placeholder: this.$interface.getText(
          "search-field-input",
          "Title"
        ),
        autocomplete: this.autocompleteSuggestions,
        onFocusOut: () => {
          console.log("onFocusOut");
          this.isAutocompleteInFocus = false;
        },
        onFocus: (searchText) => {
          console.log("onFocus", searchText);
          this.isAutocompleteInFocus = true;
          this.searchAutocomplete(searchText);
        },
        hide: () => {
          console.log("hide", this.searchTextFilters);
          this.cleanSugestions();
        },
        isActive: (item) => {
          console.log("isActive", item);
          if (this.query[item.field]) {
            return true;
          }
          return false;
        },
        click: (item) => {
          return;
        },
      });
      // Add trigger buttons to remove autocomplete selected fields
      for (var filter of this.searchTextFilters) {
        if (this.query[filter.field]) {
          var humanReg = this.query[filter.field]["$regex"];
          try {
            humanReg = this.query[filter.field]["$regex"].match(/(\w)+$/g);
          } catch (e) {
            console.log(e);
          }
          settings.push({
            type: "trigger",
            value: filter.field,
            title: filter.title + ": " + humanReg,
          });
        }
      }
      return settings;
    },
    menuId: function () {
      if (this.$helpers.args[1] !== undefined) {
        return this.$helpers.args[1];
      }
      return false;
    },
    mode: function () {
      if (this.$helpers.args[0] !== undefined) {
        return this.$helpers.args[0];
      }
      return false;
    },
  },
  methods: {
    searchAutocomplete: function (searchText) {
      this.cleanSugestions();
      if (searchText == "" || searchText === undefined) {
        return;
      }
      if (this.addressSearchTimeout !== false) {
        clearTimeout(this.addressSearchTimeout);
        this.addressSearchTimeout = false;
      }
      this.addressSearchTimeout = setTimeout(() => {
        this.addressSearchTimeout = false;
        this.searchItemsFields(searchText);
      }, 500);
    },
    searchItemsFields: function (searchText) {
      console.log("searchItemsFields", searchText, this.searchTextFilters);
      if (searchText == undefined) {
        return;
      }
      this.cleanSugestions("field");
      var fields = JSON.parse(JSON.stringify(this.searchTextFilters));
      for (var field of fields) {
        field.type = "field";
        field.typeTitle = this.$t("fields-search-group-title", "Fields");
        field.value = searchText;
        field.href = "#";
        field.title = searchText;
        field.click = (item) => {
          if (this.query[item.field]) {
            return delete this.query[item.field];
          }
          var regExpression = {
            $regex: "(^|\\s)" + item.value,
            $options: "i",
          };
          this.query[item.field] = regExpression;
        };
        this.autocompleteSuggestions.push(field);
      }
    },
    cleanSugestions: function (type) {
      if (type == undefined || type == false) {
        return (this.autocompleteSuggestions = []);
      }
      for (var i = this.autocompleteSuggestions.length - 1; i >= 0; --i) {
        if (this.autocompleteSuggestions[i].type == type) {
          this.autocompleteSuggestions.splice(i, 1);
        }
      }
    },
    clearTrigger: function (filter) {},
    clearFilter: function () {
      this.page = 0;
    },
    loadMenu: function (docID) {
      for (var i in this.menus) {
        if (this.menus[i].id == docID) {
          return (this.menu = this.menus[i]);
        }
      }
      this.$api.getCategory(docID).then((response) => {
        this.$debug.info("getCategory", docID);
        if (response.error) {
          return this.$debug.error("getCategory", docID, response);
        }
        this.menu = response.answer;
      });
    },
    unSelectMenu: function () {
      var newHash = "#list/" + this.menu.id;
      if (this.page) {
        newHash = newHash + "page=" + (this.page + 1);
      }
      window.location.href = newHash;
    },
    updateMenu: function (newmenu) {
      this.refresh();
    },
    refresh: function () {
      this.isLoading = true;
      if (this.refreshDelayTimer) {
        clearTimeout(this.refreshDelayTimer);
      }
      this.refreshDelayTimer = setTimeout(() => {
        var searchQuery = JSON.parse(JSON.stringify(this.query));
        delete searchQuery.searchText;
        var query = {
          query: searchQuery,
          limit: this.step,
          skip: this.skip,
          sort: this.sort,
        };
        if (!this.$auth.isAdmin) {
          query.query.ownerId = { $in: [0, this.$auth.User.id] };
        }
        this.menus = [];
        this.$api.getCategories(query).then((response) => {
          this.$debug.info("getCategories", query, response);
          this.isLoading = false;

          this.total = 0;
          if (response.error) {
            this.$debug.error("getCategories:error", response);
            return;
          }

          if (response.headers["x-total-count"]) {
            this.total = parseInt(response.headers["x-total-count"]);
          }
          for (var menu of response.answer) {
            this.menus.push(menu);
          }
        });
      });
    },
  },
  mounted: function () {
    this.refresh();
    if (this.menuId) {
      this.loadMenu(this.menuId);
    }
    var searchQuery = JSON.parse(JSON.stringify(this.query));
    // removing autocomplete element
    delete searchQuery.searchText;
    this.latestQuerySTR = JSON.stringify(searchQuery);
  },
};
</script>
