<style scoped>
  .filter-wrapper :deep(.filters .filter-item.filter-type) {
    display: none !important;
  }
  .filter-wrapper :deep(.filter .actions .clear-filter-button) {
    display: none;
  }
</style>

<template>
  <ItemAdd
    v-if="mode == 'add'"
    :isAttachment="isAttachment"
    :category="category"
    @cancelModal="unSelectMenu"
    @added="addedField"
  />
  <div class="row fields-view">
    <div class="p-0 filter-wrapper">
      <FilterBar
        v-model="query"
        @clearTrigger="clearTrigger"
        name="admin-menu-filter"
        :settings="settings"
        class="content-filter container-fluid border border-bottom-0 border-info rounded-top"
        @clearFilter="clearFilter"
      >
      </FilterBar>
      <ItemsView
        class="stripped"
        :listings="menus"
        :is-updating="isLoading"
        v-model:page="page"
        v-model:sort="sort"
        :sortOptions="sortOptions"
        sortLabel="Sort:"
        :total="total"
        :isHideMap="true"
        v-model:step="step"
        :stepOptions="stepOptions"
        row-class="row-cols-1"
        wrapper="container-fluid"
      >
        <template v-slot:[field.id] v-for="field in menus" :key="field.id">
          <ItemView
            :item="field"
            :category="category"
            :is-select="category ? true : false"
            :isAttachment="isAttachment"
            @fieldDeleted="refresh"
            @fieldEdited="fieldEdited"
          />
        </template>
      </ItemsView>
    </div>
  </div>
</template>
<script>
import ItemsView from "@incomnetworking/vue-component-items-list";
import FilterBar from "@incomnetworking/vue-component-filter-bar";
import ItemView from "./ItemView.vue";
import ItemAdd from "./ItemAdd.vue";
export default {
  data: function () {
    return {
      query: {
        component: {
          $in: ["vue-component-category-items"],
        },
      },
      sort: {
        changed: -1,
      },
      latestQuerySTR: "",
      isLoading: true,
      refreshTimer: false,
      refreshDelayTimer: false,
      autocompleteSuggestions: [],
      isAutocompleteInFocus: false,
      addressSearchTimeout: false,
      menus: [],
      menu: false,
      skip: 0,
      page: 0,
      step: 24,
      total: 0,
    };
  },
  props: ["category", "isAttachment"],
  emits: ["field-edited"],
  components: {
    FilterBar,
    ItemsView,
    ItemView,
    ItemAdd,
  },
  watch: {
    sort: {
      handler: function (newValue) {
        this.refresh();
      },
      deep: true,
    },
    "query.searchText": function (searchText) {
      console.log("query.searchText", searchText);
      if (this.isAutocompleteInFocus) {
        this.searchAutocomplete(searchText);
      }
    },
    query: {
      handler: function () {
        var searchQuery = JSON.parse(JSON.stringify(this.query));
        // removing autocomplete element
        delete searchQuery.searchText;

        if (this.latestQuerySTR == JSON.stringify(searchQuery)) {
          this.$debug.log("nothing changed", this.latestQuerySTR);
          return;
        }
        this.latestQuerySTR = JSON.stringify(searchQuery);
        this.refresh();
      },
      deep: true,
    },
    skip: function () {
      this.refresh();
    },
    step: function () {
      this.page = 0;
      this.refresh();
    },
    page: function (newValue) {
      this.skip = newValue * this.step;
    },
    menuId: function (newValue) {
      if (newValue && newValue != "") {
        this.loadMenu(newValue);
      }
    },
  },
  computed: {
    stepOptions: function () {
      var steps = [6, 24, 48, 96];
      var options = [];
      for (var i of steps) {
        options.push({
          value: i,
          title: i + " " + this.$interface.getText("per-page", "per page"),
        });
      }
      return options;
    },
    searchTextFilters: function () {
      var fields = [
        {
          field: "title",
          title: this.$t("Title"),
        },
      ];
      return fields;
    },
    sortOptions: function () {
      var sorts = [];
      sorts.push({
        title: this.$interface.getText("sort-by-title-newest", "By Newest"),
        value: { changed: -1 },
      });
      sorts.push({
        title: this.$interface.getText("sort-by-title-oldest", "By Oldest"),
        value: { changed: 1 },
      });
      sorts.push({
        title: this.$interface.getText("sort-by-name", "Alphabetically"),
        value: {
          name: 1,
          title: 1,
        },
      });
      return sorts;
    },
    settings: function () {
      var settings = [];
      var self = this;
      var typeOptions = [
        {
          value: undefined,
          title: this.$t("All"),
        },
        {
          value: "integer",
          title: this.$t("variable-type-integer", "Integer value"),
        },
        {
          value: "float",
          title: this.$t("variable-type-float", "Float value"),
        },
        {
          value: "string",
          title: this.$t("variable-type-string", "String value"),
        },
        {
          value: "selectString",
          title: this.$t("variable-type-select-string", "Select String value"),
        },
        {
          value: "selectNumber",
          title: this.$t("variable-type-select-number", "Select Number value"),
        },
      ];
      /* Autocomplete search box*/
      settings.push({
        type: "autocomplete",
        value: "searchText",
        placeholder: this.$interface.getText(
          "search-field-input",
          "Title"
        ),
        autocomplete: this.autocompleteSuggestions,
        onFocusOut: () => {
          console.log("onFocusOut");
          this.isAutocompleteInFocus = false;
        },
        onFocus: (searchText) => {
          console.log("onFocus", searchText);
          this.isAutocompleteInFocus = true;
          this.searchAutocomplete(searchText);
        },
        hide: () => {
          console.log("hide", this.searchTextFilters);
          this.cleanSugestions();
        },
        isActive: (item) => {
          console.log("isActive", item);
          if (this.query[item.field]) {
            return true;
          }
          return false;
        },
        click: (item) => {
          return;
        },
      });
      settings.push({
        type: "dropdown",
        value: "type",
        title: (field, filter) => {
          for (var item of filter.items) {
            if (item.value == field) {
              return item.title;
            }
          }
          return this.$interface.getText("search-fields-type", "Search Type");
        },
        items: typeOptions,
      });
      // Add trigger buttons to remove autocomplete selected fields
      for (var filter of this.searchTextFilters) {
        if (this.query[filter.field]) {
          var humanReg = this.query[filter.field]["$regex"];
          try {
            humanReg = this.query[filter.field]["$regex"].match(/(\w)+$/g);
          } catch (e) {
            console.log(e);
          }
          settings.push({
            type: "trigger",
            value: filter.field,
            title: filter.title + ": " + humanReg,
          });
        }
      }
      return settings;
    },
    menuId: function () {
      if (this.$helpers.args[1] !== undefined) {
        return this.$helpers.args[1];
      }
      return false;
    },
    mode: function () {
      if (this.$helpers.args[0] !== undefined) {
        return this.$helpers.args[0];
      }
      return false;
    },
  },
  methods: {
    addedField: function () {
      var newHash = "#";
      this.menu = false;
      window.location.href = newHash;
      this.refresh();
    },
    searchAutocomplete: function (searchText) {
      this.cleanSugestions();
      if (searchText == "" || searchText === undefined) {
        return;
      }
      if (this.addressSearchTimeout !== false) {
        clearTimeout(this.addressSearchTimeout);
        this.addressSearchTimeout = false;
      }
      this.addressSearchTimeout = setTimeout(() => {
        this.addressSearchTimeout = false;
        this.searchItemsFields(searchText);
      }, 500);
    },
    searchItemsFields: function (searchText) {
      console.log("searchItemsFields", searchText, this.searchTextFilters);
      if (searchText == undefined) {
        return;
      }
      this.cleanSugestions("field");
      var fields = JSON.parse(JSON.stringify(this.searchTextFilters));
      for (var field of fields) {
        field.type = "field";
        field.typeTitle = this.$t("fields-search-group-title", "Fields");
        field.value = searchText;
        field.href = "#";
        field.title = searchText;
        field.click = (item) => {
          if (this.query[item.field]) {
            return delete this.query[item.field];
          }
          var regExpression = {
            $regex: "(^|\\s)" + item.value,
            $options: "i",
          };
          this.query[item.field] = regExpression;
        };
        this.autocompleteSuggestions.push(field);
      }
    },
    cleanSugestions: function (type) {
      if (type == undefined || type == false) {
        return (this.autocompleteSuggestions = []);
      }
      for (var i = this.autocompleteSuggestions.length - 1; i >= 0; --i) {
        if (this.autocompleteSuggestions[i].type == type) {
          this.autocompleteSuggestions.splice(i, 1);
        }
      }
    },
    clearTrigger: function (filter) {},
    clearFilter: function () {
      this.page = 0;
    },
    loadMenu: function (docID) {
      for (var i in this.menus) {
        if (this.menus[i].id == docID) {
          return (this.menu = this.menus[i]);
        }
      }
      this.$api.getCategory(docID).then((response) => {
        this.$debug.info("getCategory", docID);
        if (response.error) {
          return this.$debug.error("getCategory", docID, response);
        }
        this.menu = response.answer;
      });
    },
    unSelectMenu: function () {
      var newHash = "#list/" + this.menu.id;
      if (this.pager) {
        newHash = newHash + "page=" + (this.pager + 1);
      }
      window.location.href = newHash;
    },
    updateMenu: function (newmenu) {
      this.refresh();
    },
    fieldEdited: function (item) {
      this.$emit("field-edited", item);
      this.refresh();
    },
    refresh: function () {
      this.isLoading = true;
      if (this.refreshDelayTimer) {
        clearTimeout(this.refreshDelayTimer);
      }
      this.refreshDelayTimer = setTimeout(() => {
        var searchQuery = JSON.parse(JSON.stringify(this.query));
        delete searchQuery.searchText;
        var query = {
          query: searchQuery,
          limit: this.step,
          skip: this.skip,
          sort: this.sort,
        };
        if (!this.$auth.isAdmin) {
          query.query.ownerId = this.$auth.User.id;
        }
        this.$api.getCategories(query).then((response) => {
          this.$debug.info("getCategories", query, response);
          this.isLoading = false;
          this.menus = [];
          this.total = 0;
          if (response.error) {
            this.$debug.error("getCategories:error", response);
            return;
          }

          if (response.headers["x-total-count"]) {
            this.total = parseInt(response.headers["x-total-count"]);
          }
          for (var menu of response.answer) {
            this.menus.push(menu);
          }
        });
      });
    },
  },
  mounted: function () {
    this.refresh();
    if (this.menuId) {
      this.loadMenu(this.menuId);
    }
    if (this.category) {
      console.log("category", this.category);
      this.query.category = { $ne: this.category.id };
    }
    var searchQuery = JSON.parse(JSON.stringify(this.query));
    // removing autocomplete element
    delete searchQuery.searchText;
    this.latestQuerySTR = JSON.stringify(searchQuery);
  },
};
</script>
