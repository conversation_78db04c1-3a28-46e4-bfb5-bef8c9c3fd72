<template>
  <Teleport to="body">
    <ModalView
      ref="modal"
      :title="title"
      :show-title="true"
      @hiddenBsModal="cancel"
      :show-on-mount="true"
      class="modal-dialog-centered modal-lg"
      :actions="[
        {
          title: this.$interface.getText('btn-modal-cancel', 'Cancel'),
          class: 'btn-secondary',
          click: function () {
            cancel();
          },
        },
        {
          title: this.$interface.getText('btn-modal-delete', 'Delete'),
          class: 'btn-danger',
          click: function () {
            deleteGroup();
          },
        },
      ]"
    >
      <div class="help-delete">
        {{
          this.$interface.getText(
            "category-delete-mesage",
            "Confirm removing this category"
          )
        }}
      </div>
      <div class="ms-2">
        <CategoryPreview :item="item" :fields="fields" />
      </div>
    </ModalView>
  </Teleport>
</template>
<script>
import ModalView from "@incomnetworking/vue-bootstrap-modal";
import CategoryPreview from "./CategoryPreview.vue";
import { deleteFile } from "@incomnetworking/vue-component-gcloud-files";
export default {
  data() {
    return {
      fields: [],
    };
  },
  components: {
    ModalView,
    CategoryPreview,
  },
  props: ["item"],
  emits: ["cancelModal", "deleted", "item-edited", "item-deleted"],
  computed: {
    title: function () {
      return this.$t("remove-category-modal-title", "Remove Category");
    },
  },
  methods: {
    loadItems: function () {
      var query = {
        category: this.item.id,
        component: {
          $in: ["vue-component-category-items"],
        },
      };
      this.$api.getCategories(query).then((response) => {
        this.$debug.info("getCategories", query, response);
        this.isLoading = false;
        if (response.error) {
          this.$debug.error("getCategories:error", response);
          return;
        }
        for (var field of response.answer) {
          this.fields.push(field);
        }
      });
    },
    keepNewLine: function (value) {
      return this.$t(value).replace(/(?:\r\n|\r|\n)/g, "<br>");
    },
    cancel: function () {
      this.$emit("cancelModal");
    },
    deleteCategoryFromItem: async function (groupName, field) {
      var update = {
        $pull: {
          category: groupName,
        },
      };
      var response = await this.$api.updateForm(field.id, update);
      this.$debug.info("updateForm", update, response);

      if (response.error) {
        return this.$debug.log("updateForm", response.error);
      }
      this.$emit("item-edited", response.answer);
      return response.answer;
    },
    deleteFile: deleteFile,
    deleteGroup: async function () {
      // delete all menus that is child for this one
      for (var field of this.fields) {
        await this.deleteCategoryFromItem(this.item.name, field);
      }
      if (this.item.fileId) {
        this.deleteFile(this.item.fileId);
      }
      this.$api.deleteCategory(this.item.id).then((response) => {
        this.$debug.info("deleteCategory", response);
        this.$emit("deleted");
      });
    },
  },
  mounted: function () {
    this.loadItems();
  },
};
</script>
