<template>
  <div class="item-list">
    <div
      class="d-flex align-items-center justify-content-between flex-row mb-2 pb-1"
    >
      <div class="ms-2">
        <div>
          {{ item.title }}
        </div>
        <div class="form-text" v-html="keepNewLine(item.description)"></div>
      </div>
    </div>
    <div class="d-flex flex-wrap gap-2">
      <div v-for="field in sortedFields" :key="field.id">
        <span
          class="badge text-bg-info disabled"
          v-h-tooltip.html="itemDescription(field)"
        >
          <span v-if="field.icon" class="me-2" v-html="field.icon" />
          {{ $t(field.title) }}
        </span>
      </div>
    </div>
    <div>
      <div
        class="badge text-bg-info ms-2"
        v-for="searchType of item.searchType"
        :key="searchType"
      >
        {{ searchTypeOptions[searchType] }}
      </div>
    </div>
  </div>
</template>
<script>
import DirectiveTooltip from "@incomnetworking/vue-directive-tooltip";
export default {
  // Properties returned from data() become reactive state
  // and will be exposed on `this`.
  data: function () {
    return {};
  },
  directives: {
    "h-tooltip": DirectiveTooltip,
  },
  props: ["item", "fields", "isAttachment"],
  watch: {},
  components: {},
  computed: {
    sortedFields: function () {
      var fieldGroupFields = this.item.fields;
      if (fieldGroupFields === undefined) {
        fieldGroupFields = {};
      }
      if (!this.fields) {
        return [];
      }
      return this.fields.sort(function (a, b) {
        var aOrder = 100;
        var bOrder = 100;
        if (fieldGroupFields[a.name] !== undefined) {
          aOrder = fieldGroupFields[a.name];
        }

        if (fieldGroupFields[b.name] !== undefined) {
          bOrder = fieldGroupFields[b.name];
        }

        if (aOrder < bOrder) {
          return -1;
        } else if (aOrder > bOrder) {
          return 1;
        }
        // a must be equal to b
        return 0;
      });
    },
    searchTypeOptions: function () {
      return {
        residential: this.$interface.getText(
          "search-type-residential",
          "Residential"
        ),
        commercial: this.$interface.getText(
          "search-type-commercial",
          "Commercial"
        ),
        condominium: this.$interface.getText(
          "search-type-condominium",
          "Condominium"
        ),
        "luxury-residential": this.$interface.getText(
          "search-type-luxury-residential",
          "Luxury Residential"
        ),
      };
    },
  },
  methods: {
    itemDescription: function (item) {
      if (item.description && item.description != "") {
        return this.$t(item.description);
      }
      return false;
    },
    keepNewLine: function (value) {
      return this.$t(value).replace(/(?:\r\n|\r|\n)/g, "<br>");
    },
  },
};
</script>
