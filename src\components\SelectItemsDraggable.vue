<style lang="scss" scoped>
.menu-row {
  margin: 0.5rem;
  padding: 0.5rem;
}
.stripped {
  --bs-table-color: var(--bs-body-color);
  --bs-table-bg: transparent;
  --bs-table-border-color: var(--bs-border-color);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: var(--bs-body-color);
  --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
  --bs-table-active-color: var(--bs-body-color);
  --bs-table-active-bg: rgba(0, 0, 0, 0.1);
  --bs-table-hover-color: var(--bs-body-color);
  --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  color: var(--bs-table-color);
  vertical-align: top;
  border-color: var(--bs-table-border-color);
}
.stripped .menu-row {
  color: var(
    --bs-table-color-state,
    var(--bs-table-color-type, var(--bs-table-color))
  );
  background-color: var(--bs-table-bg);
  border-bottom-width: var(--bs-border-width);
  box-shadow: inset 0 0 0 9999px
    var(--bs-table-bg-state, var(--bs-table-bg-type, var(--bs-table-accent-bg)));
  border-color: var(--bs-table-border-color);
}
.stripped .menu-row:nth-of-type(odd) {
  --bs-table-color-type: var(--bs-table-striped-color);
  --bs-table-bg-type: var(--bs-table-striped-bg);
}

.item {
  display: inline;
  margin-left: 1rem;
}

.handle {
  cursor: grab;
}

.sortable-chosen {
  cursor: grabbing;
}

.menu-items li {
  list-style: none;
}
.dragArea {
  min-height: 50px;
  //outline: 1px dashed;
}
</style>
<template>
  <div class="stripped menu-items">
    <draggable
      v-model="items"
      handle=".handle"
      tag="ul"
      class="dragArea"
      :item-key="getKey"
      :group="{ name: 'menu-items' }"
    >
      <template #item="{ element, index }">
        <li class="menu-row">
          <div
            class="d-flex align-items-center justify-content-between flex-row"
          >
            <div class="d-flex flex-row">
              <div class="handle">
                <i class="fa-solid fa-up-down-left-right"></i>
              </div>
              <div class="ms-2">
                <span class="link" v-h-tooltip.html="getItemTooltip(element)">
                  <span v-if="element.icon" v-html="element.icon"></span>
                  <span class="ms-2">{{ element.title }}</span>
                </span>
              </div>
            </div>

            <div>
              <div
                v-for="role of element.roles"
                class="badge text-bg-success ms-2"
              >
                {{ role }}
              </div>
            </div>
            <div>
              <div class="item" v-if="isRemoveItem !== index">
                <a class="link" href="#items" @click.prevent="AddSubItem(index)"
                  ><i class="fa-solid fa-plus"></i
                ></a>
              </div>
              <div class="item" v-if="isRemoveItem !== index">
                <a class="link" href="#items" @click.prevent="editItem(index)"
                  ><i class="fa-sharp fa-solid fa-pencil"></i
                ></a>
              </div>
              <div class="item" v-if="isRemoveItem !== index">
                <a
                  class="link"
                  href="#rooms"
                  @click.prevent="isRemoveItem = index"
                  ><i class="fa-sharp fa-solid fa-trash"></i
                ></a>
              </div>
              <div class="item" v-if="isRemoveItem === index">
                <a
                  class="link text-danger"
                  href="#rooms"
                  @click.prevent="removeItem(index)"
                  ><i class="fa-sharp fa-solid fa-trash"></i> Confirm</a
                >
              </div>
              <div class="item" v-if="isRemoveItem === index">
                <a
                  class="link text-secondary ms-2"
                  href="#rooms"
                  @click.prevent="isRemoveItem = false"
                  ><i class="fa-sharp fa-solid fa-trash"></i> Cancel</a
                >
              </div>
            </div>
          </div>
          <menu-items-draggable
            v-if="element.items !== undefined"
            v-model="element.items"
            :type="type"
            :is-sub-menu="true"
            class="bg-gray-100 pl-5"
          />
        </li>
      </template>
    </draggable>
    <SelectItemAdd
      v-if="EditItem !== false"
      :item="items[EditItem]"
      :type="type"
      @SelectItemAdded="ItemEdited"
      @cancelModal="cancelItemAdd"
    />
    <SelectItemAdd
      v-if="isAddSubItem !== false"
      :type="type"
      @SelectItemAdded="menuSubItemAdd"
      @cancelModal="isAddSubItem = false"
    />
    <SelectItemAdd
      v-if="isAddItem !== false"
      :type="type"
      @SelectItemAdded="SelectItemAdd"
      @cancelModal="isAddItem = false"
    />
    <div class="row" v-if="isAddItemButton">
      <div class="col">
        <button class="btn btn-link" @click="isAddItem = true">
          <i class="fa-solid fa-plus"></i>
          {{ $interface.getText("menu-add-item", "Add menu item") }}
        </button>
      </div>
    </div>
  </div>
</template>
<script>
import draggable from "vuedraggable";
import DirectiveTooltip from "@incomnetworking/vue-directive-tooltip";
import SelectItemAdd from "./SelectItemAdd.vue";
export default {
  data() {
    return {
      isAddItem: false,
      EditItem: false,
      isRemoveItem: false,
      isAddSubItem: false,
      items: [],
    };
  },
  directives: {
    "h-tooltip": DirectiveTooltip,
  },
  display: "Nested",
  group: "nested",
  props: ["modelValue", "isSubMenu", "type"],
  emits: ["update:modelValue"],
  components: {
    draggable,
    SelectItemAdd,
  },
  name: "menu-items-draggable",
  watch: {
    items: {
      handler: function (newValue, oldvalue) {
        console.log("watch.items", newValue);
        var newSTR = JSON.stringify(newValue);
        var origSTR = JSON.stringify(this.modelValue);

        if (newSTR != origSTR) {
          console.log("emit.update:modelValue", newValue);
          this.$emit("update:modelValue", newValue);
        }
      },
      deep: true,
    },
    modelValue: function (newValue) {
      console.log("watch.modelValue", newValue);
      var newSTR = JSON.stringify(newValue);
      var origSTR = JSON.stringify(this.value);
      if (newSTR != origSTR) {
        if (newValue !== undefined) {
          this.items = JSON.parse(JSON.stringify(newValue));
        }
      }
    },
  },
  computed: {
    isAddItemButton: function () {
      if (this.isAddItem === true) {
        return false;
      }
      if (this.isSubMenu) {
        return false;
      }
      if (this.items === undefined) {
        return true;
      }
      if (this.items.length == 0) {
        return true;
      }
      if (this.isSubMenu) {
        return false;
      }
      return true;
    },
  },
  methods: {
    getKey: function (element, index) {
      console.log("getKey", element, index);
      return element.title;
    },
    editItem: function (index) {
      this.EditItem = index;
    },
    AddSubItem: function (index) {
      this.isAddSubItem = index;
    },
    cancelItemAdd: function () {
      this.EditItem = false;
    },
    ItemEdited: function (item) {
      this.items[this.EditItem] = item;
      this.EditItem = false;
    },
    getItemTooltip: function (item) {
      if (!item.description) {
        return false;
      }
      if (
        typeof item.description === "string" ||
        item.description instanceof String
      ) {
        return this.$t(item.description);
      }
      if (item.description[this.$state.Language]) {
        return item.description[this.$state.Language];
      }
      return this.$t(item.description.default);
    },
    removeItem: function (index) {
      this.items.splice(index, 1);
      this.isRemoveItem = false;
    },
    SelectItemAdd: function (item) {
      this.items.push(item);
      this.isAddItem = false;
    },
    menuSubItemAdd: function (item) {
      if (this.items[this.isAddSubItem].items == undefined) {
        this.items[this.isAddSubItem].items = [];
      }
      this.items[this.isAddSubItem].items.push(item);
      this.isAddSubItem = false;
    },
  },
  mounted: function () {
    console.log("mounted", this.modelValue);
    if (Array.isArray(this.modelValue)) {
      this.items = JSON.parse(JSON.stringify(this.modelValue));
    }
  },
};
</script>
