<template>
  <div>
    <HeaderView :title="title">
      <span class="separator"
        ><i class="fa-solid fa-grip-lines-vertical"></i
      ></span>
      <a href="#add" class="item add-item"
        ><i class="fa-solid fa-file-circle-plus"></i
      ></a>
    </HeaderView>
    <div v-if="$api.online">
      <NavTabs class="nav-tabs" @active="setActive">
        <div
          :data-bs-title="$interface.getText('categories-title', 'Categories')"
          data-bs-target="categories"
          data-bs-active="true"
          role="tabpanel"
        >
          <CategoriesView
            v-if="tab === 'categories'"
            :is-attachment="isAttachment"
          />
        </div>
        <div
          :data-bs-title="$interface.getText('category-items-title', 'Items')"
          data-bs-target="items"
          role="tabpanel"
        >
          <ItemsView v-if="tab === 'items'" :is-attachment="isAttachment" />
        </div>
      </NavTabs>
    </div>
  </div>
</template>
<script>
import CategoriesView from "./components/CategoriesView.vue";
import ItemsView from "./components/ItemsView.vue";
import NavTabs from "@incomnetworking/vue-component-nav-tabs";
import HeaderView from "@incomnetworking/vue-component-dashboard-header";
export default {
  // Properties returned from data() become reactive state
  // and will be exposed on `this`.
  name: "vue-widget-dashboard-category",
  data() {
    return {
      tab: "categories",
    };
  },
  components: {
    CategoriesView,
    ItemsView,
    NavTabs,
    HeaderView,
  },
  computed: {
    isAttachment: function () {
      if (
        this.settings &&
        this.settings.settings &&
        this.settings.settings.attachment
      ) {
        return true;
      }
      return false;
    },
    title: function () {
      return this.$interface.getText(
        "admin-categories-title",
        "Manage Categories"
      );
    },
  },
  props: ["settings"],
  methods: {
    setActive: function (t) {
      window.location.hash = ''
      this.tab = t;
    },
  },
  widget: function (app) {
    return {
      component: "vue-widget-dashboard-category",
      name: "Dashboard Categories",
      description: "Dashboard Categories",
      icon: '<i class="fa-solid fa-list"></i>',
      settings: { attachment: true },
      order: 1,
    };
  },
  form: function (app) {
    return {
      attachment: {
        type: "form-checkbox",
        attributes: {
          label: "Enable file attachment on categories and items",
        },
        /*
        paramenters:
          value - data to validate
          formData - all form data
        */
        // eslint-disable-next-line no-unused-vars
      },
    };
  },
};
</script>
