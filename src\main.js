import { createApp } from "vue";
import Debug from "@gormartsen/vue-debug";

import App from "./App.vue";
import "@incomnetworking/vue-bootstrap-theme-homelife-dashboard/style";

var APP = createApp(App);
APP.use(Debug, true);
// Short Code names for phrases
import InterfaceTextPlugin from "@incomnetworking/interface-text";
import InterfaceBundleTranslations from "@incomnetworking/interface-translations";
APP.use(InterfaceTextPlugin, InterfaceBundleTranslations);

import VueWidgetComponent from "./index";
APP.component("VueWidgetComponent", VueWidgetComponent);

// Persist state this.$state global variable
var sessionName = "API-Browser-APP";
import defaultSettings from "./settings/state";
import PersistState from "vue-persist-state";
APP.use(PersistState, sessionName, defaultSettings);

// Dataset this.$dataset global variable
import DatasetState from "@gormartsen/vue-dataset";
import defaultDataSet from "./settings/dataset";
APP.use(DatasetState, defaultDataSet);

// API this.$api global variable
import ApiClient from "@microservice-framework/vue-api-client";
import apiSettings from "@incomnetworking/pagebuild-api-settings-dashboard";
APP.use(ApiClient, apiSettings);

import EditWrapper from "@incomnetworking/vue-edit-wrapper";
APP.use(EditWrapper);

// API this.$helpers global variable
import APPHelpers from "@incomnetworking/vue-plugin-app-helper";
APP.use(APPHelpers);

// API this.$auth global variable
import APPAuth from "@incomnetworking/vue-plugin-auth";
APP.use(APPAuth);

APP.mount("#app");

// Import scoped transaltions
import Translations from "./translation";
APP.$interface.importTranslation("vue-widget-dashboard-menus", Translations);

// Make APP accessable in browser console
window.APP = APP;
