<style lang="scss">
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  position: relative;
  font-weight: normal;
}
</style>
<template>
  <WidgetDashboardCategory
    :class="getWidgetClass(widget.state)"
    :settings="widget.settings"
    :style="getWidgetStyle(widget.state.css)"
  />
  <!-- <VueComponentEditorWrapper
    v-if="widget !== false"
    v-model:settings="widget.settings"
    v-model:state="widget.state"
    :container="[]"
    class="widget"
    ref="ref"
    targetType="widget"
  >
    <VueWidgetComponent
      :class="getWidgetClass(widget.state)"
      :settings="widget.settings"
      :style="getWidgetStyle(widget.state.css)"
    />
  </VueComponentEditorWrapper> -->
</template>
<script>
import MicroserviceClient from "@microservice-framework/microservice-client";
import VueLibrary from "@incomnetworking/page-builder-library";
import WidgetDashboardCategory from "./vue-widget-dashboard-category.vue";
export default {
  // Properties returned from data() become reactive state
  // and will be exposed on `this`.
  data() {
    return {
      widget: {
        settings:{
          component: "vue-widget-dashboard-category",
          name: "Dashboard Category List",
          description: "Dashboard Category List",
          icon: '<i class="fa-solid fa-file-lines"></i>',
          settings: { hideMap: false },
          order: 1,
        },
        state: {},
      },
    };
  },
  components : {
    WidgetDashboardCategory,
  },
  methods: {
    getWidgetStyle: function (css) {
      var style = "";
      if (css && css.background) {
        style = "background: url(" + css.background + ");";
      }
      return style;
    },
    getWidgetClass: function (item) {
      var className = "widget";
      var widget = this.widget.settings;
      if (widget.component) {
        className = className + " widget-" + widget.component;
      }
      if (widget.name) {
        className = className + " " + VueLibrary.makeSafeForCSS(widget.name);
      }
      return className;
    },
  },
  watch: {},
  mounted() {
    this.widget = {
      settings: this.$editWrapper.getWidgetInfo(
        "vue-widget-dashboard-category"
      ),
      state: {
        component: "vue-widget-dashboard-category",
      },
    };
    this.widget.settings.component = "vue-widget-dashboard-category";
    console.log("this.widget.settings", this.widget.settings);
    var AccessToken = false;
    if (window.accessToken) {
      AccessToken = window.accessToken;
    }
    if (this.$state.accessToken !== "") {
      AccessToken = this.$state.accessToken;
    }
    if (AccessToken !== false) {
      var client = new MicroserviceClient({
        URL: this.$api.url,
        accessToken: AccessToken,
        headers: { scope: "auth" },
      });
      client.get("auth/" + AccessToken).then((response) => {
        this.$debug.info("accessToken", response);
        if (response.error) {
          this.authError = response.error.message;
          return;
        }

        this.$api.setAccessToken(response.answer);
        this.$dataset.accessToken = response.answer;
        this.$state.accessToken = response.answer.accessToken;
        this.$state.expireAt = response.answer.expireAt;
        this.isLogin = true;
        this.accessToken = response.answer;
      });
    }
  },
};
</script>
