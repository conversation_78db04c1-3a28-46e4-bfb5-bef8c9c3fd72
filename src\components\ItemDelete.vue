<template>
  <Teleport to="body">
    <ModalView
      ref="modal"
      :title="title"
      :show-title="true"
      @hiddenBsModal="cancel"
      :show-on-mount="true"
      class="modal-dialog-centered modal-lg"
      :actions="[
        {
          title: this.$interface.getText('btn-modal-cancel', 'Cancel'),
          class: 'btn-secondary',
          click: function () {
            cancel();
          },
        },
        {
          title: this.$interface.getText('btn-modal-delete', 'Delete'),
          class: 'btn-danger',
          click: function () {
            processDelete();
          },
        },
      ]"
    >
      <div class="help-delete">
        {{
          this.$interface.getText(
            "item-delete-mesage",
            "Confirm removing this item "
          )
        }}
      </div>
      <div class="ms-2">
        <span
          class="badge text-bg-info disabled"
          v-h-tooltip.html="itemDescription(item)"
        >
          {{ $t(item.title) }}
        </span>
      </div>
    </ModalView>
  </Teleport>
</template>
<script>
import ModalView from "@incomnetworking/vue-bootstrap-modal";
import DirectiveTooltip from "@incomnetworking/vue-directive-tooltip";
import { deleteFile } from "@incomnetworking/vue-component-gcloud-files";

export default {
  data() {
    return {};
  },
  directives: {
    "h-tooltip": DirectiveTooltip,
  },
  components: {
    ModalView,
  },
  props: ["item", "fields", "category"],
  emits: ["cancelModal", "deleted"],
  computed: {
    title: function () {
      if (this.category) {
        return this.$t(
          "remove-item-from-category-modal-title",
          "Remove Item From Category"
        );
      }
      return (
        this.$t("remove-item-modal-title", "Remove Item") +
        " " +
        this.item.title
      );
    },
  },
  methods: {
    itemDescription: function (item) {
      if (item.description && item.description != "") {
        return this.$t(item.description);
      }
      return false;
    },
    keepNewLine: function (value) {
      return this.$t(value).replace(/(?:\r\n|\r|\n)/g, "<br>");
    },
    cancel: function () {
      this.$emit("cancelModal");
    },
    processDelete: function () {
      if (this.category === true) {
        //delete for all groups
        this.deleteItem();
        return;
      }
      if (this.category) {
        var update = {
          $pull: {
            category: this.category,
          },
        };
        this.$api.updateCategory(this.item.id, update).then((response) => {
          this.$debug.info("updateCategory", update, response);
          if (response.error) {
            return this.$debug.log("updateCategory", response.error);
          }
          this.$emit("edited", response.answer);
        });
      }
      return;
    },
    deleteFile: deleteFile,
    deleteItem: async function () {
      if (this.item.fileId) {
        this.deleteFile(this.item.fileId);
      }
      // delete all menus that is child for this one
      this.$api.deleteCategory(this.item.id).then((response) => {
        this.$debug.info("deleteCategory", response);
        this.$emit("deleted");
      });
    },
  },
};
</script>
