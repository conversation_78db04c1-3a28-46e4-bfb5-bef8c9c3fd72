{"name": "@incomnetworking/vue-widget-dashboard-category", "productName": "Category management", "description": "Category management", "version": "1.0.8", "widget": true, "license": "SEE LICENSE IN <LICENSE.txt>", "files": ["dist", "src"], "main": "./src/index.js", "module": "./src/index.js", "exports": {".": {"import": "./src/index.js", "require": "./dist/vue-widget-dashboard-category.umd.cjs"}, "./style": "./dist/style.css", "./vue": "./src/vue-widget-dashboard-category.vue", "./translation": "./src/translation.js", "./info": "./src/info.mjs"}, "scripts": {"dev": "vite dev", "build": "vite build", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix "}, "peerDependencies": {"vue": "^3.2.27"}, "devDependencies": {"@gormartsen/vue-dataset": "^1.0.1", "@incomnetworking/interface-text": "^1.0.23", "@incomnetworking/interface-translations": "^1.0.0", "@incomnetworking/page-builder-library": "^1.0.2", "@incomnetworking/pagebuild-api-settings-dashboard": "^1.0.2", "@incomnetworking/pagebuild-cdn-settings": "^1.0.1", "@incomnetworking/pagebuilder-ckeditor": "^1.0.2", "@incomnetworking/vue-bootstrap-theme-homelife-dashboard": "^1.0.21", "@incomnetworking/vue-edit-wrapper": "^1.0.10", "@incomnetworking/vue-plugin-app-helper": "^1.0.22", "@incomnetworking/vue-plugin-auth": "^1.0.3", "@microservice-framework/microservice-client": "github:microservice-framework/microservice-client#2.x", "@microservice-framework/vue-api-client": "github:microservice-framework/vue-api-client#2.x", "@rushstack/eslint-patch": "^1.1.0", "@vitejs/plugin-vue": "^3.2.0", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-external-globals": "^0.6.1", "sass": "^1.53.0", "vite": "^3.0.2", "vite-plugin-inject-externals": "^1.1.1", "vue": "^3.3.12", "vue-persist-state": "^2.0.0"}, "dependencies": {"@incomnetworking/vue-bootstrap-dropdown": "^1.0.11", "@incomnetworking/vue-bootstrap-modal": "^1.1.6", "@incomnetworking/vue-component-collapse-panel": "^1.0.2", "@incomnetworking/vue-component-dashboard-header": "^1.0.0", "@incomnetworking/vue-component-filter-bar": "^1.0.24", "@incomnetworking/vue-component-gcloud-files": "^1.0.4", "@incomnetworking/vue-component-items-list": "^1.0.7", "@incomnetworking/vue-component-nav-tabs": "^1.0.2", "@incomnetworking/vue-component-time-view": "^1.0.1", "@incomnetworking/vue-directive-tooltip": "^1.0.3", "@incomnetworking/vue-form-components": "^1.0.7", "vuedraggable": "^4.1.0"}}